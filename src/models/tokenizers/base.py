"""Base tokenizer class for audio generation tasks."""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Tuple

import torch.nn as nn
from torch import Tensor


class BaseTokenizer(nn.Module, ABC):
    """Base class for audio tokenizers.
    
    This class provides a common interface for all audio tokenizers,
    designed to work with PyTorch Lightning and Hydra configuration.
    """
    
    def __init__(self, sample_rate: int = 44100, **kwargs):
        super().__init__()
        self.sample_rate = sample_rate
        
    @abstractmethod
    def __call__(self, data: Dict[str, Any]) -> Tuple[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary containing raw audio and metadata
            
        Returns:
            target: Target tensor for training (b, c, t, f)
            cond_dict: Dictionary with conditioning tensors:
                - y: Label/class conditioning (b,) or None
                - c: Global conditioning (b, c_dim) or None  
                - ct: Temporal conditioning (b, ct_dim, t) or None
                - ctf: Temporal-frequency conditioning (b, ctf_dim, t, f) or None
                - cx: Cross-attention conditioning (b, cx_dim, seq_len) or None
            cond_sources: Dictionary with source conditioning data for visualization/debugging
        """
        pass
        
    @abstractmethod
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert latent representation back to audio.
        
        Args:
            x: Latent tensor (b, c, t, f)
            
        Returns:
            audio: Audio tensor (b, c, l)
        """
        pass
