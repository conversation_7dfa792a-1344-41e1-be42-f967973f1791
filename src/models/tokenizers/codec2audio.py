"""Codec-to-audio tokenizer for neural codec decoding tasks."""

from typing import Any, Dict, Optional, <PERSON><PERSON>

from torch import Tensor

from src.models.encoders.bigvgan import Mel_BigVGAN_44kHz
from .base import BaseTokenizer


class Codec2AudioTokenizer(BaseTokenizer):
    """Tokenizer for codec-to-audio generation tasks."""
    
    def __init__(self, sample_rate: int = 44100, n_quantizers: int = 1, **kwargs):
        super().__init__(sample_rate=sample_rate, **kwargs)
        self.n_quantizers = n_quantizers
        
        # Vocoder for audio <-> mel spectrogram conversion
        self.vocoder = Mel_BigVGAN_44kHz()
        
    def forward(self, data: Dict[str, Any]) -> Tuple[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary with keys:
                - "audio": Audio tensor (b, c, l)
                - "codec": Codec tokens (b, n_q, t) where n_q is number of quantizers
                - "dataset_name": Dataset name (b,) as strings
                
        Returns:
            target: Mel spectrogram target (b, c, t, f)
            cond_dict: Conditioning dictionary with codec tokens
            cond_sources: Source conditioning data
        """
        # Convert audio to mel spectrogram target
        audio = data["audio"]  # (b, c, l)
        target = self.vocoder.encode(audio)  # (b, c, t, f)
        
        # Get codec tokens as conditioning
        codec_tokens = data["codec"]  # (b, n_q, t)
        
        # Use only first n_quantizers if specified
        if codec_tokens.shape[1] > self.n_quantizers:
            codec_tokens = codec_tokens[:, :self.n_quantizers, :]
            
        # Create conditioning dictionary
        cond_dict = {
            "y": None,                    # No class conditioning
            "c": None,                    # No global conditioning
            "ct": codec_tokens,           # Codec tokens as temporal conditioning
            "ctf": None,                  # No temporal-frequency conditioning
            "cx": None                    # No cross-attention conditioning
        }
        
        # Source conditioning for debugging/visualization
        cond_sources = {
            "codec_tokens": codec_tokens,
            "n_quantizers": self.n_quantizers,
        }
        
        return target, cond_dict, cond_sources
        
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert mel spectrogram to audio using vocoder."""
        return self.vocoder.decode(x)
