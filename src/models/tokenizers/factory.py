"""Factory function for creating tokenizers from Hydra configs."""

from omegaconf import DictConfig
from hydra.utils import instantiate

from .base import BaseTokenizer


def create_tokenizer(config: DictConfig) -> BaseTokenizer:
    """Create a tokenizer from Hydra config.
    
    Args:
        config: Hydra config with _target_ pointing to tokenizer class
        
    Returns:
        Instantiated tokenizer
        
    Example:
        ```python
        tokenizer = create_tokenizer(cfg.tokenizer)
        target, cond_dict, cond_sources = tokenizer(batch_data)
        ```
    """
    return instantiate(config)


# Backward compatibility alias
get_tokenizer = create_tokenizer
