"""MIDI-to-audio tokenizer for piano music generation."""

from typing import Any, Dict, Optional, <PERSON><PERSON>

import torchaudio
from einops import rearrange
from torch import Tensor

from src.models.encoders.bigvgan import Mel_BigVGAN_44kHz
from .base import BaseTokenizer


class Midi2AudioTokenizer(BaseTokenizer):
    """Tokenizer for MIDI-to-audio generation.

    This tokenizer uses MIDI piano roll data as temporal conditioning
    to generate piano audio. The piano roll is resampled to match the
    target mel spectrogram temporal resolution.
    """

    def __init__(self, sample_rate: int = 44100, **kwargs: Any) -> None:
        super().__init__(sample_rate=sample_rate, **kwargs)

        # Vocoder for audio <-> mel spectrogram conversion
        self.vocoder = Mel_BigVGAN_44kHz()
        
    def forward(
        self, data: Dict[str, Any]
    ) -> Tuple[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary with keys:
                - "audio": Piano audio (b, c, l)
                - "frame_roll": Piano roll data (b, t, d) where d=128 (piano keys)
                - "dataset_name": Dataset name (b,) as strings
                
        Returns:
            target: Mel spectrogram of piano audio (b, c, t, f)
            cond_dict: Conditioning dictionary with resampled piano roll
            cond_sources: Source conditioning data
        """
        dataset_name = data["dataset_name"][0]

        if dataset_name not in ["MAESTRO"]:
            raise ValueError(f"Unsupported dataset: {dataset_name}")

        # Convert piano audio to mel spectrogram target
        audio = data["audio"]  # (b, c, l)
        device = audio.device
        target = self.vocoder.encode(audio)  # (b, c, t, f)
        
        # Process piano roll conditioning
        frame_roll = data["frame_roll"].to(device)  # (b, t, d) where d=128
        cond_t = rearrange(frame_roll, 'b t d -> b d t')  # (b, d, t)
        
        # Resample piano roll to match target temporal resolution
        cond_t = torchaudio.functional.resample(
            waveform=cond_t.contiguous(),
            orig_freq=cond_t.shape[2],
            new_freq=target.shape[2]
        )  # (b, d, t)
        
        # Create conditioning dictionary
        cond_dict = {
            "y": None,       # No label conditioning
            "c": None,       # No global conditioning
            "ct": cond_t,    # Piano roll temporal conditioning
            "ctf": None,     # No temporal-frequency conditioning
            "cx": None       # No cross-attention conditioning
        }
        
        # Source conditioning for debugging/visualization
        cond_sources = {
            "image": frame_roll,  # Original piano roll for visualization
        }
        
        return target, cond_dict, cond_sources
        
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert mel spectrogram to audio using vocoder.
        
        Args:
            x: Mel spectrogram tensor (b, c, t, f)
            
        Returns:
            audio: Audio tensor (b, c, l)
        """
        return self.vocoder.decode(x)

