"""Mono-to-stereo tokenizer for stereo audio generation."""

from dataclasses import dataclass
from typing import Any, Dict, Optional, Tuple

import torch
import torch.nn as nn
from omegaconf import DictConfig
from torch import Tensor

from src.models.encoders.bigvgan import Mel_BigVGAN_44kHz
from .base import BaseTokenizer, TokenizerConfig


@dataclass
class Mono2StereoConfig(TokenizerConfig):
    """Configuration for Mono2Stereo tokenizer."""
    name: str = "Mono2StereoTokenizer"
    sample_rate: int = 44100


class Mono2StereoTokenizer(BaseTokenizer):
    """Tokenizer for mono-to-stereo audio generation.
    
    This tokenizer uses mono audio as conditioning to generate stereo audio.
    The mono audio is encoded as a mel spectrogram condition.
    """
    
    def __init__(self, config: Mono2StereoConfig):
        super().__init__(config)
        
        # Vocoder for audio <-> mel spectrogram conversion
        self.vocoder = Mel_BigVGAN_44kHz()
        
    def __call__(self, data: Dict[str, Any]) -> <PERSON><PERSON>[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary with keys:
                - "mixture": Stereo audio mixture (b, 2, l)
                - "dataset_name": Dataset name (b,) as strings
                
        Returns:
            target: Mel spectrogram of stereo audio (b, 2, t, f)
            cond_dict: Conditioning dictionary with mono mel spectrogram
            cond_sources: Source conditioning data
        """
        dataset_name = data["dataset_name"][0]
        device = self.get_device()
        
        if dataset_name not in ["MUSDB18HQ"]:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
            
        # Convert stereo audio to mel spectrogram target
        stereo = data["mixture"].to(device)  # (b, 2, l)
        target = self.vocoder.encode(stereo)  # (b, 2, t, f)
        
        # Convert mono audio (averaged from stereo) to mel spectrogram conditioning
        mono = torch.mean(stereo, keepdim=True, dim=1)  # (b, 1, l)
        cond_tf = self.vocoder.encode(mono)  # (b, 1, t, f)
        
        # Create conditioning dictionary
        cond_dict = {
            "y": None,       # No label conditioning
            "c": None,       # No global conditioning
            "ct": None,      # No temporal conditioning
            "ctf": cond_tf,  # Mono mel spectrogram conditioning
            "cx": None       # No cross-attention conditioning
        }
        
        # Source conditioning for debugging/visualization
        cond_sources = {
            "audio": mono,
        }
        
        return target, cond_dict, cond_sources
        
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert mel spectrogram to audio using vocoder.
        
        Args:
            x: Mel spectrogram tensor (b, c, t, f)
            
        Returns:
            audio: Audio tensor (b, c, l)
        """
        return self.vocoder.decode(x)
        
    @classmethod
    def from_config(cls, config: DictConfig) -> "Mono2StereoTokenizer":
        """Create tokenizer from Hydra config."""
        tokenizer_config = Mono2StereoConfig(**config)
        return cls(tokenizer_config)
