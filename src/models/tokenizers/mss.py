"""Music source separation tokenizer."""

from typing import Any, Dict, Optional, Tu<PERSON>

from torch import Tensor

from src.models.encoders.bigvgan import Mel_BigVGAN_44kHz
from .base import BaseTokenizer


class MSSTokenizer(BaseTokenizer):
    """Tokenizer for music source separation.

    This tokenizer uses audio mixture as conditioning to generate
    isolated source tracks (e.g., vocals, drums, bass, etc.).
    """

    def __init__(self, sample_rate: int = 44100, **kwargs: Any) -> None:
        super().__init__(sample_rate=sample_rate, **kwargs)

        # Vocoder for audio <-> mel spectrogram conversion
        self.vocoder = Mel_BigVGAN_44kHz()
        
    def forward(
        self, data: Dict[str, Any]
    ) -> Tuple[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary with keys:
                - "target": Isolated source audio (b, c, l)
                - "mixture": Full audio mixture (b, c, l)
                - "dataset_name": Dataset name (b,) as strings
                
        Returns:
            target: Mel spectrogram of isolated source (b, c, t, f)
            cond_dict: Conditioning dictionary with mixture mel spectrogram
            cond_sources: Source conditioning data
        """
        dataset_name = data["dataset_name"][0]

        if dataset_name not in ["MUSDB18HQ"]:
            raise ValueError(f"Unsupported dataset: {dataset_name}")

        # Convert isolated source to mel spectrogram target
        sources = data["target"]  # (b, c, l)
        target = self.vocoder.encode(sources)  # (b, c, t, f)
        
        # Convert mixture to mel spectrogram conditioning
        mixture = data["mixture"]  # (b, c, l)
        cond_tf = self.vocoder.encode(mixture)  # (b, c, t, f)
        
        # Create conditioning dictionary
        cond_dict = {
            "y": None,       # No label conditioning
            "c": None,       # No global conditioning
            "ct": None,      # No temporal conditioning
            "ctf": cond_tf,  # Mixture mel spectrogram conditioning
            "cx": None       # No cross-attention conditioning
        }
        
        # Source conditioning for debugging/visualization
        cond_sources = {
            "audio": mixture,
        }
        
        return target, cond_dict, cond_sources
        
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert mel spectrogram to audio using vocoder.
        
        Args:
            x: Mel spectrogram tensor (b, c, t, f)
            
        Returns:
            audio: Audio tensor (b, c, l)
        """
        return self.vocoder.decode(x)

