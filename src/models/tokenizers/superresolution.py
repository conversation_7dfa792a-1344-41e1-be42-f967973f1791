"""Super-resolution tokenizer for audio upsampling tasks."""

from typing import Any, Dict, Optional, Tuple

import torchaudio.transforms as T
from torch import Tensor

from src.models.encoders.bigvgan import Mel_BigVGAN_44kHz
from .base import BaseTokenizer


class SuperResolutionTokenizer(BaseTokenizer):
    """Tokenizer for audio super-resolution tasks."""
    
    def __init__(
        self,
        sample_rate: int = 44100,
        distorted_sample_rate: int = 8000,
        **kwargs: Any
    ) -> None:
        super().__init__(sample_rate=sample_rate, **kwargs)
        self.distorted_sample_rate = distorted_sample_rate
        
        # Vocoder for high-quality audio
        self.vocoder = Mel_BigVGAN_44kHz()
        
        # Resampler for creating low-quality conditioning
        self.downsample = T.Resample(
            orig_freq=sample_rate,
            new_freq=distorted_sample_rate
        )
        self.upsample = T.Resample(
            orig_freq=distorted_sample_rate, 
            new_freq=sample_rate
        )
        
    def forward(
        self, data: Dict[str, Any]
    ) -> <PERSON><PERSON>[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary with keys:
                - "audio": High-quality audio tensor (b, c, l)
                - "dataset_name": Dataset name (b,) as strings
                
        Returns:
            target: High-quality mel spectrogram target (b, c, t, f)
            cond_dict: Conditioning dictionary with low-quality features
            cond_sources: Source conditioning data
        """
        # High-quality audio as target
        audio = data["audio"]  # (b, c, l)
        target = self.vocoder.encode(audio)  # (b, c, t, f)
        
        # Create low-quality conditioning
        low_quality = self.downsample(audio)  # (b, c, l_low)
        low_quality_upsampled = self.upsample(low_quality)  # (b, c, l)
        
        # Ensure same length as original
        if low_quality_upsampled.shape[-1] != audio.shape[-1]:
            low_quality_upsampled = low_quality_upsampled[..., :audio.shape[-1]]
            
        low_quality_features = self.vocoder.encode(low_quality_upsampled)  # (b, c, t, f)
        
        # Create conditioning dictionary
        cond_dict = {
            "y": None,                        # No class conditioning
            "c": None,                        # No global conditioning
            "ct": None,                       # No temporal conditioning  
            "ctf": low_quality_features,      # Low-quality as temporal-frequency conditioning
            "cx": None                        # No cross-attention conditioning
        }
        
        # Source conditioning for debugging/visualization
        cond_sources = {
            "low_quality_audio": low_quality_upsampled,
            "distorted_sample_rate": self.distorted_sample_rate,
        }
        
        return target, cond_dict, cond_sources
        
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert mel spectrogram to audio using vocoder."""
        return self.vocoder.decode(x)
