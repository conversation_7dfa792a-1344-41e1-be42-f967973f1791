"""Text-to-music tokenizer for genre-conditioned music generation."""

from typing import Any, Dict, Optional, Tuple

import torch
import torch.nn as nn
from audidata.datasets import GTZAN
from torch import Tensor

from src.models.encoders.bigvgan import Mel_BigVGAN_44kHz
from .base import BaseTokenizer


class Text2MusicTokenizer(BaseTokenizer):
    """Tokenizer for text-to-music generation using genre labels.
    
    This tokenizer converts audio to mel spectrograms and encodes genre labels
    as conditioning information for music generation.
    """
    
    def __init__(self, sample_rate: int = 44100, **kwargs):
        super().__init__(sample_rate=sample_rate, **kwargs)
        
        # Label mappings from GTZAN dataset
        self.lb_to_ix = GTZAN.LB_TO_IX
        self.ix_to_lb = GTZAN.IX_TO_LB
        
        # Vocoder for audio <-> mel spectrogram conversion
        self.vocoder = Mel_BigVGAN_44kHz()
        
    def __call__(self, data: Dict[str, Any]) -> <PERSON><PERSON>[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary with keys:
                - "audio": Audio tensor (b, c, l)
                - "label": Genre labels (b,) as strings
                - "dataset_name": Dataset name (b,) as strings
                
        Returns:
            target: Mel spectrogram target (b, c, t, f)
            cond_dict: Conditioning dictionary with genre labels
            cond_sources: Source conditioning data
        """
        device = self.get_device()
        dataset_name = data["dataset_name"][0]
        
        if dataset_name not in ["GTZAN"]:
            raise ValueError(f"Unsupported dataset: {dataset_name}")
            
        # Convert audio to mel spectrogram target
        audio = data["audio"].to(device)  # (b, c, l)
        target = self.vocoder.encode(audio)  # (b, c, t, f)
        
        # Encode genre labels as conditioning
        captions = data["label"]  # (b,) list of genre strings
        y = torch.LongTensor([self.lb_to_ix[label] for label in captions]).to(device)  # (b,)
        
        # Create conditioning dictionary
        cond_dict = {
            "y": y,        # Genre label conditioning
            "c": None,     # No global conditioning
            "ct": None,    # No temporal conditioning  
            "ctf": None,   # No temporal-frequency conditioning
            "cx": None     # No cross-attention conditioning
        }
        
        # Source conditioning for debugging/visualization
        cond_sources = {
            "caption": captions,
        }
        
        return target, cond_dict, cond_sources
        
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert mel spectrogram to audio using vocoder.
        
        Args:
            x: Mel spectrogram tensor (b, c, t, f)
            
        Returns:
            audio: Audio tensor (b, c, l)
        """
        return self.vocoder.decode(x)
        
    @classmethod
    def from_config(cls, config: DictConfig) -> "Text2MusicTokenizer":
        """Create tokenizer from Hydra config."""
        tokenizer_config = Text2MusicConfig(**config)
        return cls(tokenizer_config)
