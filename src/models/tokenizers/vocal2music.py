"""Vocal-to-music tokenizer for vocal-conditioned music generation."""

from typing import Any, Dict, Optional, Tuple

from torch import Tensor

from src.models.encoders.bigvgan import Mel_BigVGAN_44kHz
from .base import BaseTokenizer


class Vocal2MusicTokenizer(BaseTokenizer):
    """Tokenizer for vocal-to-music generation using vocal audio as conditioning."""
    
    def __init__(self, sample_rate: int = 44100, **kwargs):
        super().__init__(sample_rate=sample_rate, **kwargs)
        
        # Vocoder for audio <-> mel spectrogram conversion
        self.vocoder = Mel_BigVGAN_44kHz()
        
    def forward(self, data: Dict[str, Any]) -> Tuple[Tensor, Dict[str, Optional[Tensor]], Dict[str, Any]]:
        """Transform data into latent representations and conditions.
        
        Args:
            data: Input data dictionary with keys:
                - "audio": Music audio tensor (b, c, l)
                - "vocal": Vocal audio tensor (b, c, l)
                - "dataset_name": Dataset name (b,) as strings
                
        Returns:
            target: Mel spectrogram target (b, c, t, f)
            cond_dict: Conditioning dictionary with vocal features
            cond_sources: Source conditioning data
        """
        # Convert music audio to mel spectrogram target
        audio = data["audio"]  # (b, c, l)
        target = self.vocoder.encode(audio)  # (b, c, t, f)
        
        # Convert vocal audio to conditioning features
        vocal = data["vocal"]  # (b, c, l)
        vocal_features = self.vocoder.encode(vocal)  # (b, c, t, f)
        
        # Create conditioning dictionary
        cond_dict = {
            "y": None,                    # No class conditioning
            "c": None,                    # No global conditioning
            "ct": None,                   # No temporal conditioning  
            "ctf": vocal_features,        # Vocal as temporal-frequency conditioning
            "cx": None                    # No cross-attention conditioning
        }
        
        # Source conditioning for debugging/visualization
        cond_sources = {
            "vocal_audio": data["vocal"],
        }
        
        return target, cond_dict, cond_sources
        
    def latent_to_audio(self, x: Tensor) -> Tensor:
        """Convert mel spectrogram to audio using vocoder."""
        return self.vocoder.decode(x)
