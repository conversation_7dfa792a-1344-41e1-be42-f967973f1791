#!/usr/bin/env python3
"""Demonstration of __call__ vs forward in PyTorch modules."""

import torch
import torch.nn as nn


class SimpleModule(nn.Module):
    def __init__(self):
        super().__init__()
        self.linear = nn.Linear(2, 1)
        
    def forward(self, x):
        print("forward() called")
        return self.linear(x)


def demo_hooks():
    """Demonstrate that hooks only work with __call__, not forward()"""
    model = SimpleModule()
    x = torch.randn(1, 2)
    
    # Add a hook
    def my_hook(module, input, output):
        print("Hook triggered!")
    
    model.register_forward_hook(my_hook)
    
    print("=== Calling model(x) - uses __call__ ===")
    result1 = model(x)  # This triggers hooks
    
    print("\n=== Calling model.forward(x) directly ===")
    result2 = model.forward(x)  # This does NOT trigger hooks
    
    print(f"\nResults are equal: {torch.allclose(result1, result2)}")


def demo_training_mode():
    """Demonstrate training/eval mode differences"""
    model = nn.Dropout(0.5)
    x = torch.randn(10, 5)
    
    model.train()
    print("=== Training mode ===")
    print("Using model(x):")
    result1 = model(x)
    print(f"Some values are zero (dropout applied): {(result1 == 0).any()}")
    
    print("Using model.forward(x):")
    result2 = model.forward(x)
    print(f"Some values are zero (dropout applied): {(result2 == 0).any()}")


if __name__ == "__main__":
    print("Demonstrating __call__ vs forward differences:\n")
    demo_hooks()
    print("\n" + "="*50 + "\n")
    demo_training_mode()
